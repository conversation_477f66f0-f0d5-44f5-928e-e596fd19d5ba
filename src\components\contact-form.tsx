import * as React from "react"
import { Box, Text } from "./ui"
import * as styles from "./contact-form.css"

export default function ContactForm() {
  const [isSubmitted, setIsSubmitted] = React.useState(false)
  const [isSubmitting, setIsSubmitting] = React.useState(false)

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    // For Netlify forms, we can either:
    // 1. Let the form submit naturally (remove preventDefault) - Netlify handles everything
    // 2. Use JavaScript for enhanced UX while still supporting Netlify parsing

    e.preventDefault()
    setIsSubmitting(true)

    const form = e.currentTarget
    const formData = new FormData(form)

    try {
      // Submit to Netlify using the same endpoint Netlify expects
      const response = await fetch("/", {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: new URLSearchParams(formData as any).toString(),
      })

      if (response.ok) {
        setIsSubmitted(true)
        form.reset()
        // Hide success message after 5 seconds
        setTimeout(() => setIsSubmitted(false), 5000)
      } else {
        throw new Error("Form submission failed")
      }
    } catch (error) {
      alert("Une erreur s'est produite. Veuillez réessayer.")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Box paddingY={2}>
      {isSubmitted && (
        <div className={styles.successMessageStyles}>
          <Text center>
            ✅ Merci ! Votre message a été envoyé avec succès. Nous vous répondrons bientôt.
          </Text>
        </div>
      )}
      
      <form
        name="contact"
        method="POST"
        data-netlify="true"
        data-netlify-honeypot="bot-field"
        data-netlify-recaptcha="true"
        className={styles.formStyles}
        onSubmit={handleSubmit}
      >
        {/* Hidden field for Netlify */}
        <input type="hidden" name="form-name" value="contact" />
        
        {/* Honeypot field for spam protection */}
        <div style={{ display: "none" }}>
          <label>
            Don't fill this out if you're human: <input name="bot-field" />
          </label>
        </div>

        <div className={styles.formRowStyles}>
          <div className={styles.formFieldStyles}>
            <label htmlFor="name" className={styles.labelStyles}>
              Nom <span className={styles.requiredStyles}>*</span>
            </label>
            <input
              type="text"
              id="name"
              name="name"
              required
              className={styles.inputStyles}
              placeholder="Votre nom complet"
            />
          </div>

          <div className={styles.formFieldStyles}>
            <label htmlFor="email" className={styles.labelStyles}>
              Email <span className={styles.requiredStyles}>*</span>
            </label>
            <input
              type="email"
              id="email"
              name="email"
              required
              className={styles.inputStyles}
              placeholder="<EMAIL>"
            />
          </div>
        </div>

        <div>
          <label htmlFor="subject" className={styles.labelStyles}>
            Sujet <span className={styles.requiredStyles}>*</span>
          </label>
          <input
            type="text"
            id="subject"
            name="subject"
            required
            className={styles.inputStyles}
            placeholder="Objet de votre message"
          />
        </div>

        <div>
          <label htmlFor="message" className={styles.labelStyles}>
            Message <span className={styles.requiredStyles}>*</span>
          </label>
          <textarea
            id="message"
            name="message"
            required
            className={styles.textareaStyles}
            placeholder="Décrivez votre projet ou posez votre question..."
          />
        </div>

        {/* reCAPTCHA will be automatically injected here by Netlify */}
        <div data-netlify-recaptcha="true"></div>

        <Box paddingY={2}>
          <button 
            type="submit" 
            className={styles.submitButtonStyles}
            disabled={isSubmitting}
          >
            {isSubmitting ? "Envoi en cours..." : "Envoyer le message"}
          </button>
        </Box>

        <Text variant="small" center>
          <span className={styles.requiredStyles}>*</span> Champs obligatoires
        </Text>
      </form>
    </Box>
  )
}
